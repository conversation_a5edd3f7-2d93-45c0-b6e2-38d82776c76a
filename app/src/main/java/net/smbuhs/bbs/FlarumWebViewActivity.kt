package net.smbuhs.bbs

import android.annotation.TargetApi
import android.app.Activity
import android.content.ClipData
import android.content.Intent
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.View
import android.view.WindowManager
import android.webkit.*
import android.widget.ProgressBar
import android.widget.Toast
import androidx.activity.ComponentActivity
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.core.view.WindowInsetsControllerCompat
import com.tencent.smtt.export.external.interfaces.WebResourceRequest
import com.tencent.smtt.export.external.interfaces.WebResourceError
import com.tencent.smtt.sdk.WebView as X5WebView
import com.tencent.smtt.sdk.WebViewClient as X5WebViewClient
import com.tencent.smtt.sdk.WebChromeClient as X5WebChromeClient
import com.tencent.smtt.sdk.WebSettings as X5WebSettings
import com.tencent.smtt.sdk.ValueCallback as X5ValueCallback

class FlarumWebViewActivity : ComponentActivity() {

    private lateinit var webView: X5WebView
    private lateinit var progressBar: ProgressBar
    private var uploadMessage: X5ValueCallback<Array<Uri>>? = null
    
    companion object {
        private const val TAG = "FlarumWebView"
        private const val FLARUM_URL = "https://bbs.smbuhs.net"
    }
    
    // 使用新的 Activity Result API
    private val fileChooserLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        handleFileChooserResult(result.resultCode, result.data)
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 设置全屏显示，但保留状态栏
        setupSystemUI()

        // 设置布局
        setContentView(R.layout.activity_flarum_webview)

        // 初始化视图
        initViews()

        // 设置 SafeArea
        setupSafeArea()

        // 设置 WebView
        setupWebView()

        // 加载网站
        loadFlarumSite()
    }

    private fun setupSystemUI() {
        // 启用边到边显示
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            window.setDecorFitsSystemWindows(false)
        } else {
            @Suppress("DEPRECATION")
            window.decorView.systemUiVisibility = (
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            )
        }

        // 设置状态栏颜色
        window.statusBarColor = getColor(R.color.primary_color)

        // 设置状态栏文字颜色
        WindowInsetsControllerCompat(window, window.decorView).apply {
            isAppearanceLightStatusBars = false
        }
    }

    private fun initViews() {
        webView = findViewById(R.id.webView)
        progressBar = findViewById(R.id.progressBar)
    }

    private fun setupSafeArea() {
        ViewCompat.setOnApplyWindowInsetsListener(findViewById(R.id.webView)) { view, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            view.setPadding(0, systemBars.top, 0, systemBars.bottom)
            insets
        }
    }

    private fun setupWebView() {

        // 基本 X5WebView 设置
        webView.settings.apply {
            javaScriptEnabled = true
            domStorageEnabled = true
            allowFileAccess = true
            allowContentAccess = true
            setAllowFileAccessFromFileURLs(true)
            setAllowUniversalAccessFromFileURLs(true)
            // X5 内核默认允许混合内容
            cacheMode = com.tencent.smtt.sdk.WebSettings.LOAD_DEFAULT

            // 启用缩放
            setSupportZoom(true)
            builtInZoomControls = true
            displayZoomControls = false

            // 设置用户代理
            userAgentString = "$userAgentString FlarumApp/1.0 X5Core"

            // X5 特有设置
            setAppCacheEnabled(true)
            setDatabaseEnabled(true)
            setGeolocationEnabled(true)
            useWideViewPort = true
            loadWithOverviewMode = true

        }
        webView.settingsExtension.apply {
            setContentCacheEnable(true)
        }

        // 设置 X5WebViewClient
        webView.webViewClient = object : X5WebViewClient() {
            override fun shouldOverrideUrlLoading(view: X5WebView?, request: WebResourceRequest?): Boolean {
                val url = request?.url?.toString()
                return if (url != null && url.startsWith("http")) {
                    view?.loadUrl(url)
                    true
                } else {
                    false
                }
            }

            override fun onPageFinished(view: X5WebView?, url: String?) {
                super.onPageFinished(view, url)
                Log.d(TAG, "Page loaded: $url")
            }

            override fun onReceivedError(view: X5WebView?, request: WebResourceRequest?, error: WebResourceError?) {
                super.onReceivedError(view, request, error)
                Log.e(TAG, "X5WebView error: ${error?.description}")
                Toast.makeText(this@FlarumWebViewActivity, "加载失败，请检查网络连接", Toast.LENGTH_SHORT).show()
            }
        }
        
        // 设置 X5WebChromeClient 处理文件上传
        webView.webChromeClient = object : X5WebChromeClient() {
            override fun onShowFileChooser(
                webView: X5WebView?,
                filePathCallback: X5ValueCallback<Array<Uri>>?,
                fileChooserParams: com.tencent.smtt.sdk.WebChromeClient.FileChooserParams?
            ): Boolean {
                uploadMessage = filePathCallback
                openFileChooser(fileChooserParams)
                return true
            }

            override fun onProgressChanged(view: X5WebView?, newProgress: Int) {
                super.onProgressChanged(view, newProgress)
                progressBar.progress = newProgress
                progressBar.visibility = if (newProgress < 100) View.VISIBLE else View.GONE
            }
        }
    }
    
    private fun loadFlarumSite() {
        webView.loadUrl(FLARUM_URL)
    }
    
    private fun openFileChooser(fileChooserParams: com.tencent.smtt.sdk.WebChromeClient.FileChooserParams?) {
        try {
            val intent = Intent(Intent.ACTION_GET_CONTENT).apply {
                addCategory(Intent.CATEGORY_OPENABLE)
                type = "*/*"
                
                // 支持多文件选择
                putExtra(Intent.EXTRA_ALLOW_MULTIPLE, true)
                
                // 处理 accept 属性
                fileChooserParams?.acceptTypes?.let { acceptTypes ->
                    if (acceptTypes.isNotEmpty() && Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
                        putExtra(Intent.EXTRA_MIME_TYPES, acceptTypes)
                    }
                }
            }
            
            val chooserIntent = Intent.createChooser(intent, "选择文件")
            fileChooserLauncher.launch(chooserIntent)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error opening file chooser", e)
            uploadMessage?.onReceiveValue(null)
            uploadMessage = null
            Toast.makeText(this, "无法打开文件选择器", Toast.LENGTH_SHORT).show()
        }
    }
    
    private fun handleFileChooserResult(resultCode: Int, data: Intent?) {
        val results = if (resultCode == Activity.RESULT_OK && data != null) {
            getSelectedFiles(data)
        } else {
            null
        }
        
        uploadMessage?.onReceiveValue(results)
        uploadMessage = null
    }
    
    @TargetApi(Build.VERSION_CODES.LOLLIPOP)
    private fun getSelectedFiles(data: Intent): Array<Uri>? {
        return try {
            val clipData = data.clipData
            if (clipData != null) {
                // 多文件选择
                Array(clipData.itemCount) { i ->
                    clipData.getItemAt(i).uri
                }
            } else {
                // 单文件选择
                data.data?.let { arrayOf(it) }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error processing selected files", e)
            null
        }
    }
    
    override fun onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack()
        } else {
            super.onBackPressed()
        }
    }
    
    override fun onDestroy() {
        super.onDestroy()
        webView.destroy()
    }
}
